import React, { useEffect } from "react";
import { Modal, Form, Input, Button, Row, Col, Table } from "antd";
import type { OtherInfo } from "../types/task";

interface OtherInfoModalProps {
  visible: boolean;
  editingData?: OtherInfo;
  onCancel: () => void;
  onSubmit: (data: OtherInfo) => void;
}

interface SelectModalProps {
  visible: boolean;
  type: "alert" | "alertSend" | "dbConnection" | "otherInfo";
  data: any[];
  selectedData?: any[]; // 已选择的数据，用于过滤
  onCancel: () => void;
  onSubmit: (selectedItems: any[]) => void;
  multiple?: boolean;
}

// 其他信息Modal
export const OtherInfoModal: React.FC<OtherInfoModalProps> = ({
  visible,
  editingData,
  onCancel,
  onSubmit,
}) => {
  const [form] = Form.useForm();

  useEffect(() => {
    if (visible && editingData) {
      form.setFieldsValue(editingData);
    } else if (visible) {
      form.resetFields();
    }
  }, [visible, editingData, form]);

  const handleSubmit = async (values: any) => {
    const otherInfoData: OtherInfo = {
      id: editingData?.id || Date.now(),
      ...values,
    };
    onSubmit(otherInfoData);
  };

  return (
    <Modal
      title={editingData ? "编辑其他信息" : "新增其他信息"}
      open={visible}
      onCancel={onCancel}
      footer={null}
      width={600}
    >
      <Form form={form} layout="vertical" onFinish={handleSubmit}>
        <Row gutter={16}>
          <Col span={12}>
            <Form.Item
              label="信息名称"
              name="name"
              rules={[
                {
                  required: true,
                  message: "请输入信息名称",
                },
              ]}
            >
              <Input placeholder="请输入信息名称" />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              label="业务系统名称"
              name="business"
              rules={[
                {
                  required: true,
                  message: "请输入业务系统名称",
                },
              ]}
            >
              <Input placeholder="请输入业务系统名称" />
            </Form.Item>
          </Col>
        </Row>

        <Row gutter={16}>
          <Col span={12}>
            <Form.Item
              label="业务系统英文名称"
              name="businessEn"
              rules={[
                {
                  required: true,
                  message: "请输入业务系统英文名称",
                },
              ]}
            >
              <Input placeholder="请输入业务系统英文名称" />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              label="主机名称"
              name="hostname"
              rules={[
                {
                  required: true,
                  message: "请输入主机名称",
                },
              ]}
            >
              <Input placeholder="请输入主机名称" />
            </Form.Item>
          </Col>
        </Row>

        <Form.Item
          label="告警来源"
          name="location"
          rules={[
            {
              required: true,
              message: "请输入告警来源",
            },
          ]}
        >
          <Input placeholder="请输入告警来源" />
        </Form.Item>

        <div className="flex justify-end space-x-3 pt-4 border-t border-gray-200">
          <Button onClick={onCancel}>取消</Button>
          <Button type="primary" htmlType="submit">
            保存
          </Button>
        </div>
      </Form>
    </Modal>
  );
};

// 选择已有数据Modal
export const SelectModal: React.FC<SelectModalProps> = ({
  visible,
  type,
  data,
  selectedData = [],
  onCancel,
  onSubmit,
  multiple = true,
}) => {
  const [selectedRowKeys, setSelectedRowKeys] = React.useState<React.Key[]>([]);
  const [selectedRows, setSelectedRows] = React.useState<any[]>([]);
  const [pagination, setPagination] = React.useState({
    current: 1,
    pageSize: 10,
    total: 0,
  });

  // 过滤掉已选择的数据
  const filteredData = React.useMemo(() => {
    if (!selectedData || selectedData.length === 0) {
      return data;
    }
    const selectedIds = selectedData.map((item) => item.id);
    return data.filter((item) => !selectedIds.includes(item.id));
  }, [data, selectedData]);

  // 更新分页总数
  React.useEffect(() => {
    setPagination((prev) => ({
      ...prev,
      total: filteredData.length,
      current: 1, // 重置到第一页
    }));
  }, [filteredData]);

  const getColumns = () => {
    switch (type) {
      case "alert":
        return [
          {
            title: "告警名称",
            dataIndex: "name",
            key: "name",
            ellipsis: true,
          },
          {
            title: "告警级别",
            dataIndex: "severity",
            key: "severity",
            ellipsis: true,
          },
          {
            title: "告警类型",
            dataIndex: "type",
            key: "type",
            ellipsis: true,
          },
          {
            title: "SQL语句",
            dataIndex: "sql",
            key: "sql",
            ellipsis: true,
          },
        ];
      case "alertSend":
        return [
          {
            title: "发送名称",
            dataIndex: "name",
            key: "name",
            width: 150,
            ellipsis: true,
          },
          {
            title: "发送类型",
            dataIndex: "type",
            key: "type",
            width: 100,
            ellipsis: true,
            render: (type: string) =>
              type === "kafka" ? "Kafka" : "Prometheus",
          },
          {
            title: "接收地址",
            key: "address",
            width: 200,
            ellipsis: true,
            render: (_, record: any) => {
              if (record.type === "kafka" && record.kafka_receiver?.address) {
                return record.kafka_receiver.address;
              }
              if (
                record.type === "prometheus" &&
                record.prometheus_receiver?.address
              ) {
                return record.prometheus_receiver.address;
              }
              return "-";
            },
          },
          {
            title: "用户名",
            key: "username",
            width: 120,
            ellipsis: true,
            render: (_, record: any) => {
              if (record.type === "kafka" && record.kafka_receiver?.username) {
                return record.kafka_receiver.username;
              }
              if (
                record.type === "prometheus" &&
                record.prometheus_receiver?.username
              ) {
                return record.prometheus_receiver.username;
              }
              return "-";
            },
          },
          {
            title: "Topic",
            key: "topic",
            width: 120,
            ellipsis: true,
            render: (_, record: any) => {
              if (record.type === "kafka" && record.kafka_receiver?.topic) {
                return record.kafka_receiver.topic;
              }
              return "-";
            },
          },
        ];
      case "dbConnection":
        return [
          {
            title: "连接名称",
            dataIndex: "name",
            key: "name",
          },
          {
            title: "数据库类型",
            dataIndex: "db_type",
            key: "db_type",
          },
          {
            title: "主机地址",
            dataIndex: "host",
            key: "host",
          },
          {
            title: "端口",
            dataIndex: "port",
            key: "port",
          },
        ];
      case "otherInfo":
        return [
          {
            title: "信息名称",
            dataIndex: "name",
            key: "name",
          },
          {
            title: "业务系统",
            dataIndex: "business",
            key: "business",
          },
          {
            title: "主机名称",
            dataIndex: "hostname",
            key: "hostname",
          },
          {
            title: "告警来源",
            dataIndex: "location",
            key: "location",
          },
        ];
      default:
        return [];
    }
  };

  const getTitle = () => {
    switch (type) {
      case "alert":
        return "选择告警规则";
      case "alertSend":
        return "选择告警发送方式";
      case "dbConnection":
        return "选择数据库连接";
      case "otherInfo":
        return "选择其他信息";
      default:
        return "选择数据";
    }
  };

  const handleSubmit = () => {
    onSubmit(selectedRows);
    setSelectedRowKeys([]);
    setSelectedRows([]);
  };

  const handleCancel = () => {
    setSelectedRowKeys([]);
    setSelectedRows([]);
    // 重置分页状态
    setPagination({
      current: 1,
      pageSize: 10,
      total: 0,
    });
    onCancel();
  };

  // 处理分页变化
  const handleTableChange = (paginationConfig: any) => {
    setPagination({
      current: paginationConfig.current,
      pageSize: paginationConfig.pageSize,
      total: filteredData.length,
    });
  };

  const rowSelection = {
    type: multiple ? "checkbox" : "radio",
    selectedRowKeys,
    onChange: (keys: React.Key[], rows: any[]) => {
      setSelectedRowKeys(keys);
      setSelectedRows(rows);
    },
  };

  return (
    <Modal
      title={getTitle()}
      open={visible}
      onCancel={handleCancel}
      footer={
        <div className="flex justify-end space-x-3">
          <Button onClick={handleCancel}>取消</Button>
          <Button
            type="primary"
            onClick={handleSubmit}
            disabled={selectedRows.length === 0}
          >
            确定选择 ({selectedRows.length})
          </Button>
        </div>
      }
      width={1000}
    >
      {selectedData && selectedData.length > 0 && (
        <div className="mb-4 p-3 bg-blue-50 border border-blue-200 rounded-lg">
          <div className="text-sm text-blue-700">
            <span className="font-medium">提示：</span>
            已过滤掉 {selectedData.length} 个已选择的项目， 当前可选择{" "}
            {filteredData.length} 个项目
          </div>
        </div>
      )}
      <Table
        dataSource={filteredData}
        columns={getColumns()}
        rowKey="id"
        rowSelection={rowSelection as any}
        pagination={{
          current: pagination.current,
          pageSize: pagination.pageSize,
          total: pagination.total,
          showSizeChanger: true,
          showQuickJumper: true,
          showTotal: (total, range) =>
            `第 ${range[0]}-${range[1]} 条/共 ${total} 条`,
          pageSizeOptions: ["10", "20", "50", "100"],
        }}
        onChange={handleTableChange}
        scroll={{
          y: 400, // 固定表头，设置最大高度为400px
          x: "max-content", // 水平滚动
        }}
        size="small"
      />
    </Modal>
  );
};
