import { Layout, Menu } from 'antd';
import { useState } from 'react';
import { MenuFoldOutlined, MenuUnfoldOutlined, AppstoreOutlined, ExperimentOutlined } from '@ant-design/icons';
import { Outlet, useNavigate, useLocation } from 'react-router-dom';

const { Header, Content, Sider } = Layout;

export const ResponsiveLayout = () => {
  const [collapsed, setCollapsed] = useState(true);
  const navigate = useNavigate();
  const location = useLocation();

  // 根据当前路径设置选中的菜单项
  const getSelectedKey = () => {
    if (location.pathname === '/' || location.pathname === '/protable') return '1';
    if (location.pathname === '/test') return '2';
    return '1';
  };

  const handleMenuClick = ({ key }: { key: string }) => {
    if (key === '1') {
      navigate('/protable');
    } else if (key === '2') {
      navigate('/test');
    }
  };

  return (
    <Layout style={{ minHeight: '100vh' }}>
      <Sider
        width={240}
        collapsedWidth={0} // 收缩后宽度为0
        collapsed={collapsed}
        className='fixed left-0 top-0 h-screen z-50'
        trigger={null} // 禁用默认的折叠按钮
        style={{
          overflow: 'auto',
          height: '100vh',
          position: 'fixed',
          left: 0,
          top: 0,
          bottom: 0,
          zIndex: 50,
        }}
      >
        <div>
          <div>
            <span>R</span>
          </div>
          {!collapsed && <span>React Pro</span>}
        </div>

        <Menu
          mode='inline'
          selectedKeys={[getSelectedKey()]}
          onClick={handleMenuClick}
          theme='dark'
          inlineCollapsed={collapsed}
          items={[
            {
              key: '1',
              icon: <AppstoreOutlined />,
              label: 'ProTable组件',
            },
            {
              key: '2',
              icon: <ExperimentOutlined />,
              label: '重构测试',
            },
          ]}
        />

        <div onClick={() => setCollapsed(!collapsed)}>
          <div>{collapsed ? <MenuUnfoldOutlined /> : <MenuFoldOutlined />}</div>
        </div>
      </Sider>

      <Layout
        style={{
          marginLeft: collapsed ? 0 : 240,
          transition: 'margin-left 0.2s',
          minHeight: '100vh',
        }}
      >
        <Header
          style={{
            backgroundColor: '#1f2937',
            paddingLeft: collapsed ? 16 : 10,
            transition: 'padding-left 0.2s',
            position: 'sticky',
            top: 0,
            zIndex: 40,
          }}
        >
          <div
            style={{
              display: 'flex',
              alignItems: 'center',
              height: '100%',
            }}
          >
            <div
              onClick={() => setCollapsed(!collapsed)}
              style={{
                width: 32,
                height: 32,
                borderRadius: 6,
                backgroundColor: 'rgba(255, 255, 255, 0.1)',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                cursor: 'pointer',
                marginRight: 16,
                transition: 'all 0.2s',
                border: '1px solid rgba(255, 255, 255, 0.2)',
              }}
              onMouseEnter={e => {
                e.currentTarget.style.backgroundColor = 'rgba(255, 255, 255, 0.2)';
                e.currentTarget.style.transform = 'scale(1.05)';
              }}
              onMouseLeave={e => {
                e.currentTarget.style.backgroundColor = 'rgba(255, 255, 255, 0.1)';
                e.currentTarget.style.transform = 'scale(1)';
              }}
            >
              {collapsed ? (
                <MenuUnfoldOutlined
                  style={{
                    color: 'white',
                    fontSize: 16,
                  }}
                />
              ) : (
                <MenuFoldOutlined
                  style={{
                    color: 'white',
                    fontSize: 16,
                  }}
                />
              )}
            </div>
            <h1 className='text-white m-0'>React ProTable Demo</h1>
          </div>
        </Header>

        <Content
          style={{
            padding: '0',
            overflow: 'auto',
            height: 'calc(100vh - 64px)',
          }}
        >
          <div
            style={{
              overflow: 'auto',
              height: '100%',
            }}
          >
            <Outlet />
          </div>
        </Content>
      </Layout>
    </Layout>
  );
};
